import logging
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes
from typing import Dict, List, Any, Optional

from config.settings import OWNER_ID, OWNER_USER_NAME
from models.database import (
    is_user_approved, is_user_rejected, approve_user, reject_user,
    get_approved_users, get_rejected_users, can_receive_trade,
    get_trades_reset_time, MAX_TRADES_PER_DAY
)

logger = logging.getLogger(__name__)

async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send a greeting message when the command /start is issued."""
    user = update.effective_user
    user_id = user.id

    # Check if user is already approved or rejected
    if is_user_approved(user_id):
        await update.message.reply_text(
            f"Welcome back, {user.first_name}! You are already approved to receive signals.\n\n"
            f"Use the /trade command to request a high-quality trade signal.\n\n"
            f"Make sure to turn on notifications to receive trade signals.\n"
            f"Type /help for guidance on how to use the signals."
        )
        return

    if is_user_rejected(user_id):
        await update.message.reply_text(
            f"Hello {user.first_name}, your access request was previously declined. "
            f"If you believe this is an error, please contact {OWNER_USER_NAME} for assistance."
        )
        return

    # Send greeting message to user
    await update.message.reply_text(
        f"👋 Hello {user.first_name}! Welcome to the Trading Signals Bot.\n\n"
        f"This bot provides high-quality trading signals.\n\n"
        f"Your request for access is being reviewed by the admin.\n"
        f"For any questions, please contact {OWNER_USER_NAME}.\n\n"
        f"Please wait for approval to start receiving signals."
    )

    # Collect user data
    user_data = {
        'id': user.id,
        'first_name': user.first_name,
        'last_name': user.last_name,
        'username': user.username,
        'language_code': user.language_code,
        'is_bot': user.is_bot,
        'is_premium': getattr(user, 'is_premium', False)
    }

    # Create inline keyboard for owner to approve/reject
    keyboard = [
        [
            InlineKeyboardButton("✅ Approve", callback_data=f"approve_{user.id}"),
            InlineKeyboardButton("❌ Reject", callback_data=f"reject_{user.id}")
        ]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # Send user details to owner
    await context.bot.send_message(
        chat_id=OWNER_ID,
        text=(
            f"New User Registration:\n\n"
            f"User ID: {user.id}\n"
            f"First Name: {user.first_name}\n"
            f"Last Name: {user.last_name or 'N/A'}\n"
            f"Username: @{user.username or 'N/A'}\n"
            f"Language: {user.language_code or 'N/A'}\n\n"
            f"Do you want to approve this user?"
        ),
        reply_markup=reply_markup
    )

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Send a help message when the command /help is issued."""
    user_id = update.effective_user.id

    # Check if user is the owner
    if str(user_id) == str(OWNER_ID):
        # Send owner help message
        await update.message.reply_text(
            "<b>OWNER COMMANDS</b>\n\n"
            "<b>User Management:</b>\n"
            "/user - Manage approved and rejected users\n"
            "/start - Register as a user (also used by regular users)\n\n"

            "<b>System Management:</b>\n"
            "/status - Check the status of the trade checker\n\n"

            "<b>User Commands:</b>\n"
            "/help - Show this help message\n"
            "/trade - Request a high-quality trade\n"
            "/cancel - Cancel the current operation\n\n"

            "<b>System Information:</b>\n"
            "- The system uses a dual-terminal architecture\n"
            "- The bot_server.py handles Telegram interactions\n"
            "- The trade_checker.py analyzes market conditions\n"
            "- Users are limited to 3 trades per day\n"
            "- All trades are based on ICT, SMC, PA, S/R and pattern recognition concepts\n",
            parse_mode='HTML'
        )
        return

    # Check if user is approved
    if not is_user_approved(user_id):
        await update.message.reply_text(
            "You need to be approved to access this command. "
            f"Please contact {OWNER_USER_NAME} for assistance."
        )
        return

    # Get user's trade limit information
    can_receive, trades_remaining = can_receive_trade(user_id)
    trades_used = MAX_TRADES_PER_DAY - trades_remaining
    reset_time = get_trades_reset_time()

    # Send user help message with HTML formatting
    await update.message.reply_text(
        "<b>TRADING SIGNALS GUIDE</b>\n\n"
        "<b>Available Commands:</b>\n"
        "/start - Register or check your status\n"
        "/help - Show this help message\n"
        "/trade - Request a high-quality trade\n"
        "/cancel - Cancel the current operation\n\n"

        "<b>1. Signal Format:</b>\n"
        "Our signals follow this format:\n"
        "- Pair: BTC/USDT\n"
        "- Direction: BUY/SELL\n"
        "- Entry: Price range\n"
        "- Targets: Multiple price points\n"
        "- Stop Loss: Exit price if trade goes against you\n\n"

        "<b>2. Risk Management:</b>\n"
        "- Never risk more than 1-2% of your total capital per trade\n"
        "- Always set stop losses to limit potential losses\n"
        "- Don't chase entries if you missed the initial entry zone\n"
        "- Consider taking partial profits at different targets\n\n"

        "<b>3. Best Practices:</b>\n"
        "- Keep a trading journal to track your performance\n"
        "- Be patient and disciplined with entries and exits\n"
        "- Don't overtrade or use excessive leverage\n"
        "- Remember that No signal has a 100% success rate\n\n"

        f"<b>4. Daily Trade Limit:</b>\n"
        f"- You are limited to {MAX_TRADES_PER_DAY} trades per day\n"
        f"- You have used {trades_used} of your {MAX_TRADES_PER_DAY} trades today\n"
        f"- Trades remaining today: {trades_remaining}\n"
        f"- Your trade count will reset at {reset_time}\n\n"

        "<b>5. Signal Quality:</b>\n"
        "- Signals are based on multiple trading concepts (ICT, SMC, PA, S/R, Pattern Recognition)\n"
        "- Each signal includes a detailed explanation of why the trade is recommended\n\n"

        "For additional questions, contact the admin: "
        f"{OWNER_USER_NAME}",
        parse_mode='HTML'
    )

async def user_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Handle the /user command (owner only)."""
    user_id = update.effective_user.id

    # Check if the command is from the owner
    if user_id != OWNER_ID:
        await update.message.reply_text(
            "This command is only available to the bot owner."
        )
        return

    # Create inline keyboard with options for approved and rejected users
    keyboard = [
        [
            InlineKeyboardButton("Approved Users", callback_data="list_approved_users"),
            InlineKeyboardButton("Rejected Users", callback_data="list_rejected_users")
        ]
    ]
    reply_markup = InlineKeyboardMarkup(keyboard)

    # Send message with options
    await update.message.reply_text(
        "Select a user category to manage:",
        reply_markup=reply_markup
    )

async def cancel_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """Cancel the current operation."""
    user_id = update.effective_user.id

    # Check if user is in the middle of an operation
    if context.user_data.get('waiting_for_message'):
        # Reset conversation state
        context.user_data['waiting_for_message'] = False
        context.user_data['message_to_user_id'] = None

        await update.message.reply_text(
            "Operation cancelled."
        )
        return

    # No active operation to cancel
    await update.message.reply_text(
        "No active operation to cancel."
    )
